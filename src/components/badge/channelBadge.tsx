import { Badge, type BadgeConfig } from "../common";

export type ChanneType = "facebook" | "line" | "instagram" | "tiktok";

interface ChannelBadgeConfig extends BadgeConfig {
  type: ChanneType;
}

const CHANNEL_BADGE_CONFIG: Record<ChanneType, ChannelBadgeConfig> = {
  facebook: {
    containerClasses: "text-blue-500 border-blue-500",
    icon: <i className="ri-facebook-circle-fill text-xl" />,
    iconAlt: "Facebook",
    type: "facebook",
  },
  instagram: {
    containerClasses: "text-pink-500 border-pink-500",
    icon: <i className="ri-instagram-fill text-xl" />,
    iconAlt: "Instagram",
    type: "instagram",
  },
  line: {
    containerClasses: "text-green-500 border-green-500",
    icon: <i className="ri-line-fill text-xl" />,
    iconAlt: "Line",
    type: "line",
  },
  tiktok: {
    containerClasses: "text-black border-black",
    icon: <i className="ri-tiktok-fill text-xl" />,
    iconAlt: "Tiktok",
    type: "tiktok",
  },
};

interface ChannelBadgeProps {
  type?: ChanneType;
  className?: string;
}

export const ChannelBadge = ({ type, className }: ChannelBadgeProps) => {
  return (
    <Badge type={type} className={className} config={CHANNEL_BADGE_CONFIG} />
  );
};

export const _CONTACT_CHANNEL_OPTIONS = (
  ["line", "facebook", "instagram", "tiktok"] as ChanneType[]
).map((type) => ({
  label: <ChannelBadge type={type} />,
  value: type,
}));
